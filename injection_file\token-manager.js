/**
 * 简化版Token管理器 - 无感换号核心模块
 * 基于success case的核心逻辑，简化实现
 * 新增：手动输入 accessToken 和 tenantURL 的界面功能
 */

const vscode = require('vscode');

class SimpleTokenManager {
    // ========== 配置区域 - 方便修改 ==========
    static API_BASE_URL = 'https://augment-auto.techexpresser.com';  // 您的后端地址
    static AUTH_PASSWORD = 'your_secret_password_here_change_this';   // 您的认证密码
    static DEFAULT_TENANT_URL = 'https://d5.api.augmentcode.com/';
    static SCOPES = ['email'];    // Strictly align with success case 2
    // =====================================

    /**
     * 获取设置（从globalState读取，影响官方插件行为）
     */
    async getSettings() {
        try {
            const apiBaseUrl = await this.context.globalState.get('apiBaseUrl') || SimpleTokenManager.API_BASE_URL;
            const defaultTenantUrl = await this.context.globalState.get('defaultTenantUrl') || SimpleTokenManager.DEFAULT_TENANT_URL;

            return {
                success: true,
                settings: {
                    apiBaseUrl,
                    defaultTenantUrl
                }
            };
        } catch (error) {
            this.logger.error('获取设置失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 保存设置（保存到globalState，影响官方插件行为）
     */
    async saveSettings(settings) {
        try {
            if (settings.apiBaseUrl) {
                await this.context.globalState.update('apiBaseUrl', settings.apiBaseUrl);
            }
            if (settings.defaultTenantUrl) {
                await this.context.globalState.update('defaultTenantUrl', settings.defaultTenantUrl);
            }

            this.logger.info('设置保存成功');
            return {
                success: true,
                message: '设置保存成功！'
            };
        } catch (error) {
            this.logger.error('保存设置失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 重置设置为默认值
     */
    async resetSettings() {
        try {
            await this.context.globalState.update('apiBaseUrl', undefined);
            await this.context.globalState.update('defaultTenantUrl', undefined);

            this.logger.info('设置已重置为默认值');
            return {
                success: true,
                message: '设置已重置为默认值！'
            };
        } catch (error) {
            this.logger.error('重置设置失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 测试API连接（影响官方插件的网络配置）
     */
    async testApiConnection() {
        try {
            const settingsResult = await this.getSettings();
            const apiBaseUrl = settingsResult.success ? settingsResult.settings.apiBaseUrl : SimpleTokenManager.API_BASE_URL;

            const response = await fetch(`${apiBaseUrl}/api/tokens`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${SimpleTokenManager.AUTH_PASSWORD}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'VSCode-SimpleTokenManager/1.0.0'
                },
                timeout: 5000
            });

            if (response.ok) {
                return {
                    success: true,
                    message: 'API连接成功！'
                };
            } else {
                return {
                    success: false,
                    message: `API连接失败: HTTP ${response.status}`
                };
            }
        } catch (error) {
            this.logger.error('测试API连接失败', error);
            return {
                success: false,
                message: `API连接失败: ${error.message}`
            };
        }
    }

    constructor() {
        this.context = null;
        this.logger = {
            info: (msg) => console.log(`[TokenManager] ${msg}`),
            error: (msg, err) => console.error(`[TokenManager] ${msg}`, err),
            warn: (msg) => console.warn(`[TokenManager] ${msg}`)
        };
    }

    async initialize(context) {
        this.context = context;
        this.registerCommands();
        this.createWebviewProvider();
        this.logger.info('Token Manager initialized');
    }

    /**
     * 创建 webview 提供者
     */
    createWebviewProvider() {
        try {
            // 注册 webview 视图提供者
            const provider = vscode.window.registerWebviewViewProvider(
                'simpleTokenManager',
                {
                    resolveWebviewView: (webviewView) => {
                        this.webviewView = webviewView;
                        webviewView.webview.options = {
                            enableScripts: true,
                            localResourceRoots: [this.context.extensionUri]
                        };
                        webviewView.webview.html = this.getWebviewContent();

                        // 处理来自 webview 的消息
                        webviewView.webview.onDidReceiveMessage(
                            async (message) => {
                                await this.handleWebviewMessage(message);
                            },
                            undefined,
                            this.context.subscriptions
                        );
                    }
                },
                {
                    webviewOptions: {
                        retainContextWhenHidden: true
                    }
                }
            );

            this.context.subscriptions.push(provider);
            this.logger.info('Webview provider registered');
        } catch (error) {
            this.logger.error('Failed to create webview provider:', error);
        }
    }

    /**
     * 注册VSCode命令
     */
    registerCommands() {
        if (!this.context) {
            this.logger.error('Context not available for command registration');
            return;
        }

        const commands = [
            {
                id: 'simple-token-manager.manualInput',
                handler: () => this.handleManualInput()
            },
            {
                id: 'simple-token-manager.quickUpdate',
                handler: () => this.handleQuickUpdate()
            },
            {
                id: 'simple-token-manager.testConnection',
                handler: () => this.handleTestConnection()
            }
        ];

        commands.forEach(cmd => {
            try {
                const disposable = vscode.commands.registerCommand(cmd.id, cmd.handler);
                this.context.subscriptions.push(disposable);
                this.logger.info(`Registered command: ${cmd.id}`);
            } catch (error) {
                this.logger.error(`Failed to register command ${cmd.id}:`, error);
            }
        });

        this.logger.info(`Registered ${commands.length} commands`);
    }

    /**
     * 测试API连接
     */
    async testApiConnection() {
        try {
            const settingsResult = await this.getSettings();
            const apiBaseUrl = settingsResult.success ? settingsResult.settings.apiBaseUrl : SimpleTokenManager.API_BASE_URL;

            this.logger.info(`测试API连接: ${apiBaseUrl}`);

            // 先测试健康检查端点
            try {
                const healthResponse = await fetch(`${apiBaseUrl}/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'VSCode-SimpleTokenManager/1.0.0'
                    },
                    timeout: 5000
                });
                this.logger.info(`健康检查响应: ${healthResponse.status}`);
            } catch (healthError) {
                this.logger.warn(`健康检查失败: ${healthError.message}`);
            }

            // 测试认证端点
            const response = await fetch(`${apiBaseUrl}/api/tokens`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${SimpleTokenManager.AUTH_PASSWORD}`,
                    'Content-Type': 'application/json',
                    'User-Agent': 'VSCode-SimpleTokenManager/1.0.0'
                },
                timeout: 10000
            });

            return {
                success: response.ok,
                status: response.status,
                statusText: response.statusText,
                url: `${apiBaseUrl}/api/tokens`
            };

        } catch (error) {
            this.logger.error('API连接测试失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取可用的token列表
     */
    async getAvailableTokens() {
        try {
            // 使用动态设置，影响官方插件行为
            const settingsResult = await this.getSettings();
            const apiBaseUrl = settingsResult.success ? settingsResult.settings.apiBaseUrl : SimpleTokenManager.API_BASE_URL;

            // 🔍 调试信息
            this.logger.info(`正在调用API: ${apiBaseUrl}/api/tokens`);
            this.logger.info(`使用认证密码: ${SimpleTokenManager.AUTH_PASSWORD.substring(0, 10)}...`);

            const requestHeaders = {
                'Authorization': `Bearer ${SimpleTokenManager.AUTH_PASSWORD}`,
                'Content-Type': 'application/json',
                'User-Agent': 'VSCode-SimpleTokenManager/1.0.0'
            };

            this.logger.info(`请求头: ${JSON.stringify(requestHeaders, null, 2)}`);

            const response = await fetch(`${apiBaseUrl}/api/tokens`, {
                method: 'GET',
                headers: requestHeaders,
                timeout: 10000
            });

            this.logger.info(`响应状态: ${response.status} ${response.statusText}`);

            if (!response.ok) {
                // 尝试读取错误响应体
                let errorBody = '';
                try {
                    errorBody = await response.text();
                    this.logger.error(`错误响应体: ${errorBody}`);
                } catch (e) {
                    this.logger.error('无法读取错误响应体');
                }
                throw new Error(`HTTP ${response.status}: ${response.statusText}. 响应: ${errorBody}`);
            }

            const data = await response.json();

            // 根据您的API文档，返回格式是单个token对象，不是数组
            if (data.success && data.token) {
                this.logger.info(`获取到可用token: ${data.token.id || 'unknown'}`);
                return {
                    success: true,
                    tokens: [data.token] // 将单个token包装成数组以保持兼容性
                };
            } else {
                this.logger.warn('API返回无可用token');
                return {
                    success: false,
                    error: data.message || 'No available tokens',
                    tokens: []
                };
            }
        } catch (error) {
            this.logger.error('获取token列表失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 设置Secret的辅助方法（参考success case 2）
     */
    async setSecret(key, value) {
        try {
            const valueStr = typeof value === 'string' ? value : JSON.stringify(value);
            await this.context.secrets.store(key, valueStr);
            return true;
        } catch (error) {
            this.logger.error(`Failed to set secret ${key}:`, error);
            return false;
        }
    }

    /**
     * 注入token到VSCode secrets - 核心功能（完全按照success case 2实现）
     * 采用增量更新策略，保留现有session中的重要字段
     */
    async injectToken(accessToken, tenantURL = null) {
        try {
            // 使用动态设置，影响官方插件行为
            const settingsResult = await this.getSettings();
            const defaultTenantUrl = settingsResult.success ? settingsResult.settings.defaultTenantUrl : SimpleTokenManager.DEFAULT_TENANT_URL;

            // 🔑 关键修复：完全按照success case 2的方式获取和保留现有数据
            const currentValue = await this.context.secrets.get('augment.sessions');
            let currentData = {};

            if (currentValue) {
                try {
                    currentData = JSON.parse(currentValue);
                    this.logger.info('保留现有session数据，进行增量更新');
                } catch (error) {
                    this.logger.warn('解析现有session数据失败，创建新对象', error);
                    currentData = {};
                }
            } else {
                this.logger.info('未找到现有session数据，创建新对象');
            }

            // 🎯 完全按照success case 2的方式：保留所有现有字段，只更新必要的字段
            const updatedData = {
                ...currentData,  // 保留所有现有字段
                accessToken: accessToken,
                tenantURL: tenantURL || defaultTenantUrl
            };

            // Strictly align with success case 2: scopes must be ["email"]
            updatedData.scopes = ["email"];

            // 关键：使用success case 2相同的保存方式
            const success = await this.setSecret('augment.sessions', updatedData);
            if (!success) {
                throw new Error('保存会话数据失败');
            }

            this.logger.info('Token注入成功（完全按照success case方式）');
            return {
                success: true,
                data: updatedData
            };
        } catch (error) {
            this.logger.error('Token注入失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取当前存储的token信息
     */
    async getCurrentToken() {
        try {
            const sessionData = await this.context.secrets.get('augment.sessions');
            if (sessionData) {
                const data = JSON.parse(sessionData);
                return {
                    success: true,
                    accessToken: data.accessToken,
                    tenantURL: data.tenantURL,
                    data: data
                };
            } else {
                return {
                    success: false,
                    error: '未找到token信息'
                };
            }
        } catch (error) {
            this.logger.error('获取当前token失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 复制accessToken到剪贴板（影响官方插件的认证）
     */
    async copyAccessToken() {
        try {
            const currentResult = await this.getCurrentToken();
            if (!currentResult.success) {
                return currentResult;
            }

            const accessToken = currentResult.data.accessToken;
            await vscode.env.clipboard.writeText(accessToken);

            return {
                success: true,
                message: 'AccessToken已复制到剪贴板'
            };
        } catch (error) {
            this.logger.error('复制AccessToken失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 手动设置accessToken（影响官方插件认证）
     */
    async setAccessToken(accessToken) {
        try {
            if (!accessToken || accessToken.trim().length === 0) {
                return {
                    success: false,
                    error: 'AccessToken不能为空'
                };
            }

            // 获取当前会话数据，保留tenantURL和scopes
            const currentResult = await this.getCurrentToken();
            let tenantURL = SimpleTokenManager.DEFAULT_TENANT_URL;
            let scopes = SimpleTokenManager.SCOPES;

            if (currentResult.success) {
                tenantURL = currentResult.data.tenantURL || tenantURL;
                scopes = currentResult.data.scopes || scopes;
            }

            // 注入新的accessToken
            return await this.injectToken(accessToken.trim(), tenantURL);
        } catch (error) {
            this.logger.error('设置AccessToken失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取或生成设备码
     */
    async getDeviceId() {
        try {
            let deviceId = await this.context.globalState.get('sessionId');
            if (!deviceId) {
                deviceId = require('crypto').randomUUID();
                await this.context.globalState.update('sessionId', deviceId);
                this.logger.info(`生成新设备码: ${deviceId}`);
            }
            return {
                success: true,
                deviceId: deviceId
            };
        } catch (error) {
            this.logger.error('获取设备码失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 更新设备码
     */
    async updateDeviceId() {
        try {
            const newDeviceId = require('crypto').randomUUID();
            await this.context.globalState.update('sessionId', newDeviceId);
            this.logger.info(`设备码已更新: ${newDeviceId}`);
            return {
                success: true,
                deviceId: newDeviceId,
                message: `设备码已更新: ${newDeviceId}`
            };
        } catch (error) {
            this.logger.error('更新设备码失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 一键更新token - 自动选择第一个可用token
     */
    async quickUpdateToken() {
        try {
            const tokensResult = await this.getAvailableTokens();
            if (!tokensResult.success || !tokensResult.tokens.length) {
                return {
                    success: false,
                    error: '没有可用的token'
                };
            }

            // 选择第一个token
            const selectedToken = tokensResult.tokens[0];
            const injectResult = await this.injectToken(
                selectedToken.accessToken,
                selectedToken.tenantURL
            );

            if (injectResult.success) {
                this.logger.info(`一键更新成功，使用token: ${selectedToken.id || 'unknown'}`);

                // Align with your requirement: delay 1s after session update, then update machine code (sessionId)
                await new Promise(resolve => setTimeout(resolve, 1000));
                const deviceUpdate = await this.updateDeviceId();
                const message = deviceUpdate.success
                    ? '🎉 一键更新成功！已更新会话数据与设备码，建议重载窗口以生效'
                    : `部分成功：会话数据已更新，但设备码更新失败：${deviceUpdate.error}`;

                return {
                    success: true,
                    message,
                    data: injectResult.data
                };
            } else {
                return injectResult;
            }
        } catch (error) {
            this.logger.error('一键更新失败', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 处理手动输入命令
     */
    async handleManualInput() {
        try {
            // 显示主菜单选项
            const action = await vscode.window.showQuickPick([
                {
                    label: '获取当前 Token',
                    description: '查看当前的 accessToken 和 tenantURL',
                    detail: '显示当前存储的认证信息，支持复制和查看完整数据'
                },
                {
                    label: '设置 accessToken',
                    description: '手动输入新的 accessToken',
                    detail: '仅更新 accessToken，保留其他会话信息'
                },
                {
                    label: '更新会话数据',
                    description: '同时更新 accessToken 和 tenantURL',
                    detail: '完整更新：通过引导输入同时修改 tenantURL 和 accessToken'
                },
                {
                    label: '更新设备码',
                    description: '重置设备唯一标识符',
                    detail: '生成并更新当前设备的机器码标识'
                }
            ], {
                placeHolder: '选择要执行的操作'
            });

            if (!action) return;

            // 根据选择执行对应的操作
            switch (action.label) {
                case '获取当前 Token':
                    await this.handleGetCurrentToken();
                    break;
                case '设置 accessToken':
                    await this.handleSetAccessToken();
                    break;
                case '更新会话数据':
                    await this.handleUpdateSessionData();
                    break;
                case '更新设备码':
                    await this.handleUpdateMachineCode();
                    break;
            }
        } catch (error) {
            this.logger.error('Manual input error:', error);
            vscode.window.showErrorMessage(`错误: ${error.message}`);
        }
    }

    /**
     * 获取当前Token信息
     */
    async handleGetCurrentToken() {
        try {
            const result = await this.getCurrentToken();

            if (result.success) {
                const tokenDisplay = result.accessToken && result.accessToken.length > 16
                    ? `${result.accessToken.substring(0, 8)}...${result.accessToken.substring(result.accessToken.length - 8)}`
                    : result.accessToken || '未设置';

                const message = `accessToken: ${tokenDisplay}\ntenantURL: ${result.tenantURL || '未设置'}`;

                const action = await vscode.window.showInformationMessage(
                    message,
                    '复制 accessToken',
                    '显示完整数据'
                );

                if (action === '复制 accessToken' && result.accessToken) {
                    await vscode.env.clipboard.writeText(result.accessToken);
                    vscode.window.showInformationMessage('accessToken 已复制到剪贴板');
                } else if (action === '显示完整数据') {
                    const doc = await vscode.workspace.openTextDocument({
                        content: JSON.stringify(result.data, null, 2),
                        language: 'json'
                    });
                    await vscode.window.showTextDocument(doc);
                }
            } else {
                vscode.window.showErrorMessage(`获取 Token 失败: ${result.error}`);
            }
        } catch (error) {
            this.logger.error('Get current token error:', error);
            vscode.window.showErrorMessage(`错误: ${error.message}`);
        }
    }

    /**
     * 设置accessToken
     */
    async handleSetAccessToken() {
        try {
            // 获取当前的 accessToken 作为 placeholder
            let currentAccessToken = '输入新的 accessToken...';
            try {
                const currentResult = await this.getCurrentToken();
                if (currentResult.success && currentResult.accessToken) {
                    const token = currentResult.accessToken;
                    if (token.length > 16) {
                        currentAccessToken = `当前: ${token.substring(0, 8)}...${token.substring(token.length - 8)}`;
                    } else {
                        currentAccessToken = `当前: ${token}`;
                    }
                }
            } catch (error) {
                this.logger.debug('Failed to get current accessToken for placeholder:', error);
            }

            // 输入新的accessToken
            const newAccessToken = await vscode.window.showInputBox({
                prompt: '输入新的 accessToken',
                placeHolder: currentAccessToken,
                password: true,
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return 'accessToken 不能为空';
                    }
                    if (value.length < 10) {
                        return 'accessToken 长度似乎太短';
                    }
                    return null;
                }
            });

            if (!newAccessToken) return;

            // 使用现有的setAccessToken方法
            const result = await this.setAccessToken(newAccessToken.trim());
            if (result.success) {
                vscode.window.showInformationMessage('accessToken 更新成功！');

                // 发送成功消息到 webview
                this.sendMessageToWebview({
                    command: 'showStatus',
                    data: { message: 'accessToken 更新成功！', type: 'success' }
                });

                // 自动刷新 webview 中的 token 信息
                await this.handleGetCurrentTokenForWebview();

                // 显示更新后的完整数据
                const showData = await vscode.window.showInformationMessage(
                    'accessToken 更新成功！',
                    '显示更新后的数据'
                );

                if (showData === '显示更新后的数据') {
                    const doc = await vscode.workspace.openTextDocument({
                        content: JSON.stringify(result.data, null, 2),
                        language: 'json'
                    });
                    await vscode.window.showTextDocument(doc);
                }
            } else {
                vscode.window.showErrorMessage(`更新 accessToken 失败: ${result.error}`);

                // 发送错误消息到 webview
                this.sendMessageToWebview({
                    command: 'showStatus',
                    data: { message: `更新 accessToken 失败: ${result.error}`, type: 'error' }
                });
            }
        } catch (error) {
            this.logger.error('Set access token error:', error);
            vscode.window.showErrorMessage(`错误: ${error.message}`);
        }
    }

    /**
     * 更新会话数据（tenantURL + accessToken）
     */
    async handleUpdateSessionData() {
        try {
            // 获取当前的 sessions 数据作为默认值
            let currentData = {
                accessToken: '',
                tenantURL: SimpleTokenManager.DEFAULT_TENANT_URL,
                scopes: SimpleTokenManager.SCOPES
            };

            try {
                const currentResult = await this.getCurrentToken();
                if (currentResult.success) {
                    currentData = { ...currentData, ...currentResult.data };
                }
            } catch (error) {
                this.logger.debug('Failed to get current sessions data:', error);
            }

            // 第一步：输入 tenantURL
            const newTenantURL = await vscode.window.showInputBox({
                prompt: '输入 tenantURL',
                placeHolder: `当前: ${currentData.tenantURL}`,
                value: currentData.tenantURL,
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return 'tenantURL 不能为空';
                    }
                    try {
                        new URL(value);
                        return null;
                    } catch {
                        return '请输入有效的URL (例如: https://d5.api.augmentcode.com/)';
                    }
                }
            });

            if (!newTenantURL) return;

            // 第二步：输入 accessToken
            const currentTokenDisplay = currentData.accessToken && currentData.accessToken.length > 16
                ? `${currentData.accessToken.substring(0, 8)}...${currentData.accessToken.substring(currentData.accessToken.length - 8)}`
                : currentData.accessToken || '输入新的 accessToken...';

            const newAccessToken = await vscode.window.showInputBox({
                prompt: '输入 accessToken',
                placeHolder: `当前: ${currentTokenDisplay}`,
                password: true,
                validateInput: (value) => {
                    if (!value || value.trim().length === 0) {
                        return 'accessToken 不能为空';
                    }
                    if (value.length < 10) {
                        return 'accessToken 长度似乎太短';
                    }
                    return null;
                }
            });

            if (!newAccessToken) return;

            // 使用现有的injectToken方法
            const result = await this.injectToken(newAccessToken.trim(), newTenantURL.trim());
            if (result.success) {
                vscode.window.showInformationMessage('会话数据更新成功！');

                // 发送成功消息到 webview
                this.sendMessageToWebview({
                    command: 'showStatus',
                    data: { message: '会话数据更新成功！', type: 'success' }
                });

                // 自动刷新 webview 中的 token 信息
                await this.handleGetCurrentTokenForWebview();

                // 显示更新后的数据
                const showData = await vscode.window.showInformationMessage(
                    '会话数据更新成功！',
                    '显示更新后的数据'
                );

                if (showData === '显示更新后的数据') {
                    const doc = await vscode.workspace.openTextDocument({
                        content: JSON.stringify(result.data, null, 2),
                        language: 'json'
                    });
                    await vscode.window.showTextDocument(doc);
                }
            } else {
                vscode.window.showErrorMessage(`更新会话数据失败: ${result.error}`);

                // 发送错误消息到 webview
                this.sendMessageToWebview({
                    command: 'showStatus',
                    data: { message: `更新会话数据失败: ${result.error}`, type: 'error' }
                });
            }
        } catch (error) {
            this.logger.error('Update session data error:', error);
            vscode.window.showErrorMessage(`错误: ${error.message}`);
        }
    }

    /**
     * 处理快速更新命令
     */
    async handleQuickUpdate() {
        try {
            const result = await this.quickUpdateToken();
            if (result.success) {
                vscode.window.showInformationMessage(result.message);
            } else {
                vscode.window.showErrorMessage(`更新失败: ${result.error}`);
            }
        } catch (error) {
            this.logger.error('Quick update error:', error);
            vscode.window.showErrorMessage(`错误: ${error.message}`);
        }
    }

    /**
     * 处理测试连接命令
     */
    async handleTestConnection() {
        try {
            const result = await this.testApiConnection();
            if (result.success) {
                vscode.window.showInformationMessage(`API连接成功！状态: ${result.status}`);
            } else {
                vscode.window.showErrorMessage(`API连接失败: ${result.error}`);
            }
        } catch (error) {
            this.logger.error('Test connection error:', error);
            vscode.window.showErrorMessage(`错误: ${error.message}`);
        }
    }

    /**
     * 处理更新机器码命令（参考 success case 2）
     */
    async handleUpdateMachineCode() {
        try {
            // 使用uuid生成新的sessionId
            const newSessionId = require('crypto').randomUUID();

            // 直接更新sessionId
            await this.context.globalState.update('sessionId', newSessionId);

            this.logger.info(`机器码已更新: ${newSessionId}`);

            // 发送成功消息到 webview
            this.sendMessageToWebview({
                command: 'showStatus',
                data: { message: `机器码更新成功！新值: ${newSessionId}`, type: 'success' }
            });

            // 显示成功消息，参考 success case 的实现
            const selection = await vscode.window.showInformationMessage(
                `sessionId更新成功！新值: ${newSessionId}，请重载窗口以生效`,
                '重载窗口'
            );

            // 如果用户点击"重载窗口"
            if (selection === '重载窗口') {
                vscode.commands.executeCommand('workbench.action.reloadWindow');
            }
        } catch (error) {
            this.logger.error('Update machine code error:', error);
            vscode.window.showErrorMessage(`错误: ${error.message}`);
        }
    }

    /**
     * 处理来自 webview 的消息
     */
    async handleWebviewMessage(message) {
        try {
            switch (message.command) {
                case 'updateAllTokenData':
                    await this.handleUpdateAllTokenData(message.data);
                    break;
                case 'loadCurrentTokenToInputs':
                    await this.handleLoadCurrentTokenToInputs();
                    break;
                case 'getCurrentToken':
                    await this.handleGetCurrentTokenForWebview();
                    break;
                case 'quickUpdate':
                    await this.handleQuickUpdateForWebview();
                    break;
                case 'testConnection':
                    await this.handleTestConnectionForWebview();
                    break;
                default:
                    this.logger.warn(`Unknown webview command: ${message.command}`);
            }
        } catch (error) {
            this.logger.error('Webview message handling error:', error);
            this.sendMessageToWebview({
                command: 'showStatus',
                data: { message: `错误: ${error.message}`, type: 'error' }
            });
        }
    }

    /**
     * 发送消息到 webview
     */
    sendMessageToWebview(message) {
        if (this.webviewView && this.webviewView.webview) {
            this.webviewView.webview.postMessage(message);
        }
    }

    /**
     * 为 webview 处理获取当前 Token
     */
    async handleGetCurrentTokenForWebview() {
        try {
            const result = await this.getCurrentToken();

            if (result.success) {
                const tokenDisplay = result.accessToken && result.accessToken.length > 16
                    ? `${result.accessToken.substring(0, 8)}...${result.accessToken.substring(result.accessToken.length - 8)}`
                    : result.accessToken || '未设置';

                const info = `accessToken: ${tokenDisplay}\ntenantURL: ${result.tenantURL || '未设置'}`;

                this.sendMessageToWebview({
                    command: 'updateTokenInfo',
                    data: { info }
                });

                this.sendMessageToWebview({
                    command: 'showStatus',
                    data: { message: 'Token 信息已更新', type: 'success' }
                });
            } else {
                this.sendMessageToWebview({
                    command: 'showStatus',
                    data: { message: `获取 Token 失败: ${result.error}`, type: 'error' }
                });
            }
        } catch (error) {
            this.logger.error('Get current token for webview error:', error);
            this.sendMessageToWebview({
                command: 'showStatus',
                data: { message: `错误: ${error.message}`, type: 'error' }
            });
        }
    }

    /**
     * 为 webview 处理快速更新
     */
    async handleQuickUpdateForWebview() {
        try {
            this.sendMessageToWebview({
                command: 'showStatus',
                data: { message: '正在更新 Token...', type: 'success' }
            });

            const result = await this.quickUpdateToken();
            if (result.success) {
                this.sendMessageToWebview({
                    command: 'showStatus',
                    data: { message: result.message, type: 'success' }
                });
                // 自动刷新 token 信息
                await this.handleGetCurrentTokenForWebview();
            } else {
                this.sendMessageToWebview({
                    command: 'showStatus',
                    data: { message: `更新失败: ${result.error}`, type: 'error' }
                });
            }
        } catch (error) {
            this.logger.error('Quick update for webview error:', error);
            this.sendMessageToWebview({
                command: 'showStatus',
                data: { message: `错误: ${error.message}`, type: 'error' }
            });
        }
    }

    /**
     * 为 webview 处理测试连接
     */
    async handleTestConnectionForWebview() {
        try {
            this.sendMessageToWebview({
                command: 'showStatus',
                data: { message: '正在测试连接...', type: 'success' }
            });

            const result = await this.testApiConnection();
            if (result.success) {
                this.sendMessageToWebview({
                    command: 'showStatus',
                    data: { message: `API连接成功！状态: ${result.status}`, type: 'success' }
                });
            } else {
                this.sendMessageToWebview({
                    command: 'showStatus',
                    data: { message: `API连接失败: ${result.error}`, type: 'error' }
                });
            }
        } catch (error) {
            this.logger.error('Test connection for webview error:', error);
            this.sendMessageToWebview({
                command: 'showStatus',
                data: { message: `错误: ${error.message}`, type: 'error' }
            });
        }
    }

    /**
     * 处理一键更新所有 Token 数据（Token + URL + 机器码）
     */
    async handleUpdateAllTokenData(data) {
        try {
            const { accessToken, tenantUrl } = data;

            this.sendMessageToWebview({
                command: 'showStatus',
                data: { message: '正在更新 Token、URL 和机器码...', type: 'success' }
            });

            // 1. 更新会话数据（accessToken + tenantURL）
            const updateResult = await this.injectToken(accessToken, tenantUrl);
            if (!updateResult.success) {
                throw new Error(`更新会话数据失败: ${updateResult.error}`);
            }

            // 2. 更新机器码
            const newSessionId = require('crypto').randomUUID();
            await this.context.globalState.update('sessionId', newSessionId);
            this.logger.info(`机器码已更新: ${newSessionId}`);

            // 3. 发送成功消息
            this.sendMessageToWebview({
                command: 'showStatus',
                data: { message: `✅ 全部更新成功！Token、URL 和机器码已更新`, type: 'success' }
            });

            // 4. 自动刷新显示的 token 信息
            await this.handleGetCurrentTokenForWebview();

            // 5. 显示详细信息给用户
            vscode.window.showInformationMessage(
                `🎉 全部更新成功！\n` +
                `✅ accessToken: 已更新\n` +
                `✅ tenantURL: ${tenantUrl}\n` +
                `✅ 机器码: ${newSessionId.substring(0, 8)}...`
            );

        } catch (error) {
            this.logger.error('Update all token data error:', error);
            this.sendMessageToWebview({
                command: 'showStatus',
                data: { message: `❌ 更新失败: ${error.message}`, type: 'error' }
            });
            vscode.window.showErrorMessage(`更新失败: ${error.message}`);
        }
    }

    /**
     * 加载当前 Token 信息到输入框
     */
    async handleLoadCurrentTokenToInputs() {
        try {
            const result = await this.getCurrentToken();

            if (result.success) {
                this.sendMessageToWebview({
                    command: 'loadTokenToInputs',
                    data: {
                        accessToken: result.accessToken || '',
                        tenantUrl: result.tenantURL || SimpleTokenManager.DEFAULT_TENANT_URL
                    }
                });
            }
        } catch (error) {
            this.logger.error('Load current token to inputs error:', error);
            // 静默失败，不显示错误消息
        }
    }

    /**
     * 获取 webview 的 HTML 内容
     */
    getWebviewContent() {
        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline';">
    <title>Token Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 16px;
            background-color: var(--vscode-sideBar-background);
            color: var(--vscode-sideBar-foreground);
            font-size: 13px;
        }

        .section {
            margin-bottom: 20px;
            padding: 12px;
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--vscode-textLink-foreground);
            font-size: 14px;
        }

        .button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
            margin-bottom: 8px;
            width: 100%;
        }

        .button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .button.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .button.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        .token-info {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            background-color: var(--vscode-textCodeBlock-background);
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
            word-break: break-all;
            margin-bottom: 8px;
        }

        .status {
            padding: 8px;
            border-radius: 3px;
            margin-bottom: 12px;
            font-size: 12px;
            display: none;
        }

        .status.success {
            background-color: var(--vscode-testing-iconPassed);
            color: white;
        }

        .status.error {
            background-color: var(--vscode-testing-iconFailed);
            color: white;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            font-size: 12px;
        }

        .form-group input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 3px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-size: 12px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div id="status" class="status"></div>

    <!-- 手动输入区域 -->
    <div class="section">
        <div class="section-title">🔑 手动输入 Token</div>

        <div class="form-group">
            <label for="accessTokenInput">AccessToken:</label>
            <input type="password" id="accessTokenInput" placeholder="输入您的 accessToken..." />
        </div>

        <div class="form-group">
            <label for="tenantUrlInput">TenantURL:</label>
            <input type="text" id="tenantUrlInput" placeholder="https://d5.api.augmentcode.com/" />
        </div>

        <button class="button" onclick="updateAllTokenData()">� 一键更新 Token + URL + 机器码</button>
        <button class="button secondary" onclick="getCurrentToken()">� 查看当前 Token</button>
    </div>

    <!-- 快速操作 -->
    <div class="section">
        <div class="section-title">⚡ 快速操作</div>
        <button class="button" onclick="quickUpdate()">🚀 一键更新Token</button>
        <button class="button secondary" onclick="testConnection()">🔗 测试连接</button>
    </div>

    <!-- 当前Token信息 -->
    <div class="section">
        <div class="section-title">📋 当前Token信息</div>
        <div id="currentTokenInfo" class="token-info">点击"查看当前 Token"获取信息</div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        function showStatus(message, type = 'success') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = \`status \${type}\`;
            statusEl.style.display = 'block';

            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }

        function updateAllTokenData() {
            const accessToken = document.getElementById('accessTokenInput').value.trim();
            const tenantUrl = document.getElementById('tenantUrlInput').value.trim();

            // 输入验证
            if (!accessToken) {
                showStatus('请输入 accessToken', 'error');
                return;
            }

            if (accessToken.length < 10) {
                showStatus('accessToken 长度似乎太短', 'error');
                return;
            }

            if (!tenantUrl) {
                showStatus('请输入 tenantURL', 'error');
                return;
            }

            // 验证 URL 格式
            try {
                new URL(tenantUrl);
            } catch (e) {
                showStatus('请输入有效的 tenantURL (例如: https://d5.api.augmentcode.com/)', 'error');
                return;
            }

            // 发送更新请求
            vscode.postMessage({
                command: 'updateAllTokenData',
                data: {
                    accessToken: accessToken,
                    tenantUrl: tenantUrl
                }
            });

            showStatus('正在更新 Token、URL 和机器码...', 'success');
        }

        function getCurrentToken() {
            vscode.postMessage({ command: 'getCurrentToken' });
        }

        function quickUpdate() {
            vscode.postMessage({ command: 'quickUpdate' });
        }

        function testConnection() {
            vscode.postMessage({ command: 'testConnection' });
        }

        // 加载当前的 token 信息到输入框
        function loadCurrentTokenToInputs() {
            vscode.postMessage({ command: 'loadCurrentTokenToInputs' });
        }

        // 监听来自后端的消息
        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.command) {
                case 'showStatus':
                    showStatus(message.data.message, message.data.type);
                    break;
                case 'updateTokenInfo':
                    const tokenInfoEl = document.getElementById('currentTokenInfo');
                    if (tokenInfoEl) {
                        tokenInfoEl.textContent = message.data.info;
                    }
                    break;
                case 'loadTokenToInputs':
                    // 加载当前 token 信息到输入框
                    if (message.data.accessToken) {
                        document.getElementById('accessTokenInput').value = message.data.accessToken;
                    }
                    if (message.data.tenantUrl) {
                        document.getElementById('tenantUrlInput').value = message.data.tenantUrl;
                    }
                    break;
            }
        });

        // 页面加载时自动加载当前 token 信息到输入框
        setTimeout(() => {
            loadCurrentTokenToInputs();
        }, 500);
    </script>
</body>
</html>`;
    }

    dispose() {
        this.logger.info('Token Manager disposed');
    }
}

module.exports = SimpleTokenManager;
