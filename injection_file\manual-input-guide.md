# 手动输入 AccessToken 和 TenantURL 使用指南

## 🎯 功能概述

基于 success case 的分析，我们在 Simple Token Manager 中添加了完整的手动输入功能，允许用户直接输入 accessToken 和 tenantURL，无需依赖第三方 API。

## 🚀 快速开始

### 1. 安装和注入

按照 `injection-guide.md` 完成代码注入后，重启 VSCode。

### 2. 访问手动输入功能

有两种方式访问新功能：

#### 方式一：通过命令面板（推荐）
1. 按 `Ctrl+Shift+P`（Windows/Linux）或 `Cmd+Shift+P`（Mac）
2. 输入 `Simple Token Manager: Manual Input`
3. 选择并执行命令

#### 方式二：通过其他命令
- `Simple Token Manager: Quick Update` - 一键更新（从 API 获取）
- `Simple Token Manager: Test Connection` - 测试 API 连接

## 📋 功能详解

### 主菜单选项

执行 `Manual Input` 命令后，会显示以下选项：

```
┌─────────────────────────────────────────────────────────────┐
│ 选择要执行的操作                                              │
├─────────────────────────────────────────────────────────────┤
│ 📋 获取当前 Token                                            │
│    查看当前的 accessToken 和 tenantURL                       │
│    显示当前存储的认证信息，支持复制和查看完整数据               │
├─────────────────────────────────────────────────────────────┤
│ 🔑 设置 accessToken                                         │
│    手动输入新的 accessToken                                  │
│    仅更新 accessToken，保留其他会话信息                       │
├─────────────────────────────────────────────────────────────┤
│ 🔄 更新会话数据                                              │
│    同时更新 accessToken 和 tenantURL                        │
│    完整更新：通过引导输入同时修改 tenantURL 和 accessToken     │
├─────────────────────────────────────────────────────────────┤
│ 🔧 更新设备码                                                │
│    重置设备唯一标识符                                         │
│    生成并更新当前设备的机器码标识                             │
└─────────────────────────────────────────────────────────────┘
```

### 1. 获取当前 Token

- **功能**：查看当前存储的认证信息
- **显示**：accessToken（脱敏显示）和 tenantURL
- **操作**：
  - 复制 accessToken 到剪贴板
  - 在新文档中查看完整的 JSON 数据

### 2. 设置 accessToken

- **功能**：仅更新 accessToken，保留其他会话信息
- **输入验证**：
  - 不能为空
  - 长度至少 10 个字符
- **特性**：
  - 密码输入模式（隐藏输入内容）
  - 显示当前 token 的脱敏版本作为提示
  - 保留现有的 tenantURL 和 scopes

### 3. 更新会话数据

- **功能**：同时更新 accessToken 和 tenantURL
- **流程**：
  1. 输入 tenantURL（带 URL 格式验证）
  2. 输入 accessToken（密码模式）
- **验证**：
  - tenantURL 必须是有效的 URL 格式
  - accessToken 长度验证
- **特性**：
  - 显示当前值作为默认值
  - 完整的输入验证

### 4. 更新设备码

- **功能**：生成新的设备唯一标识符
- **用途**：防封机制，避免设备指纹被追踪
- **实现**：使用 `crypto.randomUUID()` 生成新的 sessionId

## 🔧 技术实现

### 数据存储方式

所有数据都存储在 VSCode 的 secrets API 中：

```javascript
// 存储位置
await context.secrets.store('augment.sessions', JSON.stringify({
    accessToken: "你的_JWT_token",
    tenantURL: "https://d5.api.augmentcode.com/",
    scopes: ["email"]
}));
```

### 增量更新策略

为了保持与原插件的兼容性，采用增量更新策略：

1. 先获取现有的会话数据
2. 只更新必要的字段
3. 保留其他重要信息（如用户 ID、权限等）

## 🛡️ 安全特性

1. **密码输入模式**：accessToken 输入时不显示明文
2. **脱敏显示**：显示 token 时只显示前 8 位和后 8 位
3. **输入验证**：防止输入无效数据
4. **错误隔离**：不影响原插件正常运行

## 📝 使用示例

### 示例 1：手动设置 accessToken

1. 执行 `Simple Token Manager: Manual Input`
2. 选择 "设置 accessToken"
3. 输入你的 JWT token
4. 确认更新成功

### 示例 2：完整更新会话数据

1. 执行 `Simple Token Manager: Manual Input`
2. 选择 "更新会话数据"
3. 输入 tenantURL（如：`https://d5.api.augmentcode.com/`）
4. 输入 accessToken
5. 确认更新成功

## 🔍 故障排除

### 常见问题

1. **命令找不到**：
   - 确保已正确注入代码并重启 VSCode
   - 检查开发者工具控制台是否有错误

2. **输入验证失败**：
   - accessToken 长度至少 10 个字符
   - tenantURL 必须是有效的 URL 格式

3. **更新后仍然 401 错误**：
   - 确认 token 是有效的 JWT 格式
   - 检查 tenantURL 是否正确
   - 尝试重启 VSCode 使更改生效

### 调试方法

1. 打开开发者工具（`Help > Toggle Developer Tools`）
2. 查看控制台日志
3. 使用 "获取当前 Token" 功能验证数据是否正确存储

## 🎉 总结

新的手动输入功能提供了完整的 token 管理界面，用户可以：

- ✅ 手动输入 accessToken 和 tenantURL
- ✅ 查看和复制当前认证信息
- ✅ 更新设备码进行防封
- ✅ 通过命令面板快速访问所有功能

这个实现完全基于 success case 的分析，提供了与成功案例相同的功能和用户体验。
