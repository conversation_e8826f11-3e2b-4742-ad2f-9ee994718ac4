/**
 * 自定义功能模块 - 提供UI界面和用户交互
 * 基于success case简化实现
 */

const vscode = require('vscode');
const SimpleTokenManager = require('./token-manager');

class CustomFeatures {
    constructor() {
        this.context = null;
        this.tokenManager = null;
        this.webviewPanel = null;
        this.logger = {
            info: (msg) => console.log(`[CustomFeatures] ${msg}`),
            error: (msg, err) => console.error(`[CustomFeatures] ${msg}`, err),
            warn: (msg) => console.warn(`[CustomFeatures] ${msg}`)
        };
    }

    async initialize(context) {
        this.context = context;
        this.tokenManager = new SimpleTokenManager();
        await this.tokenManager.initialize(context);

        // 注册命令
        this.registerCommands();

        // 创建状态栏按钮（替代侧边栏视图）
        this.createStatusBarButton();

        // 注册webview provider（用于弹窗）
        this.registerWebviewProvider();

        this.logger.info('Custom Features initialized');
    }

    registerCommands() {
        // 注册一键更新命令
        const quickUpdateCommand = vscode.commands.registerCommand('simple.token.quickUpdate', async () => {
            const result = await this.tokenManager.quickUpdateToken();
            if (result.success) {
                vscode.window.showInformationMessage(result.message);
            } else {
                vscode.window.showErrorMessage(`更新失败: ${result.error}`);
            }
        });

        // 注册打开管理界面命令
        const openManagerCommand = vscode.commands.registerCommand('simple.token.openManager', () => {
            this.showTokenManager();
        });

        // 注册测试API连接命令
        const testConnectionCommand = vscode.commands.registerCommand('simple.token.testConnection', async () => {
            vscode.window.showInformationMessage('正在测试API连接...');
            const result = await this.tokenManager.testApiConnection();
            if (result.success) {
                vscode.window.showInformationMessage(`API连接成功! 状态: ${result.status}`);
            } else {
                vscode.window.showErrorMessage(`API连接失败: ${result.error || result.statusText}`);
            }
        });

        // 注册更新设备码命令
        const updateDeviceCommand = vscode.commands.registerCommand('simple.token.updateDevice', async () => {
            const result = await this.tokenManager.updateDeviceId();
            if (result.success) {
                // 与success case一致：提示重载窗口
                const action = await vscode.window.showInformationMessage(
                    result.message + '，请重载窗口以生效',
                    '重载窗口'
                );
                if (action === '重载窗口') {
                    vscode.commands.executeCommand('workbench.action.reloadWindow');
                }
            } else {
                vscode.window.showErrorMessage(`设备码更新失败: ${result.error}`);
            }
        });

        this.context.subscriptions.push(quickUpdateCommand, openManagerCommand, testConnectionCommand, updateDeviceCommand);
    }

    createStatusBarButton() {
        // 创建状态栏按钮
        this.statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this.statusBarItem.text = "$(key) Token Manager";
        this.statusBarItem.tooltip = "Simple Token Manager - 点击打开管理界面";
        this.statusBarItem.command = 'simple.token.openManager';
        this.statusBarItem.show();

        // 添加到订阅列表，确保正确清理
        this.context.subscriptions.push(this.statusBarItem);

        this.logger.info('状态栏按钮已创建');
    }

    registerWebviewProvider() {
        // 注册webview provider
        const provider = vscode.window.registerWebviewViewProvider('simpleTokenManager', {
            resolveWebviewView: (webviewView) => {
                webviewView.webview.options = {
                    enableScripts: true
                };
                webviewView.webview.html = this.getWebviewContent();

                // 处理webview消息
                webviewView.webview.onDidReceiveMessage(async (message) => {
                    await this.handleWebviewMessage(message, webviewView.webview);
                });
            }
        });

        this.context.subscriptions.push(provider);
    }

    async showTokenManager() {
        if (this.webviewPanel) {
            this.webviewPanel.reveal();
            return;
        }

        this.webviewPanel = vscode.window.createWebviewPanel(
            'simpleTokenManager',
            'Simple Token Manager',
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true
            }
        );

        this.webviewPanel.webview.html = this.getWebviewContent();

        // 处理webview消息
        this.webviewPanel.webview.onDidReceiveMessage(async (message) => {
            await this.handleWebviewMessage(message, this.webviewPanel.webview);
        });

        // 清理
        this.webviewPanel.onDidDispose(() => {
            this.webviewPanel = null;
        });
    }

    async handleWebviewMessage(message, webview) {
        try {
            switch (message.command) {
                case 'getTokens':
                    const tokensResult = await this.tokenManager.getAvailableTokens();
                    webview.postMessage({
                        command: 'tokensResult',
                        data: tokensResult
                    });
                    break;

                case 'getCurrentToken':
                    const currentResult = await this.tokenManager.getCurrentToken();
                    webview.postMessage({
                        command: 'currentTokenResult',
                        data: currentResult
                    });
                    break;

                case 'injectToken':
                    const injectResult = await this.tokenManager.injectToken(
                        message.accessToken,
                        message.tenantURL
                    );
                    webview.postMessage({
                        command: 'injectResult',
                        data: injectResult
                    });
                    if (injectResult.success) {
                        vscode.window.showInformationMessage('Token更新成功！');
                    }
                    break;

                case 'quickUpdate':
                    const quickResult = await this.tokenManager.quickUpdateToken();
                    webview.postMessage({
                        command: 'quickUpdateResult',
                        data: quickResult
                    });
                    if (quickResult.success) {
                        vscode.window.showInformationMessage(quickResult.message);
                    }
                    break;

                case 'getDeviceId':
                    const deviceResult = await this.tokenManager.getDeviceId();
                    webview.postMessage({
                        command: 'deviceIdResult',
                        data: deviceResult
                    });
                    break;

                case 'updateDeviceId':
                    const updateDeviceResult = await this.tokenManager.updateDeviceId();
                    webview.postMessage({
                        command: 'updateDeviceResult',
                        data: updateDeviceResult
                    });
                    if (updateDeviceResult.success) {
                        vscode.window.showInformationMessage(updateDeviceResult.message);
                    }
                    break;

                // 设置相关操作（影响官方插件）
                case 'getSettings':
                    const settingsResult = await this.tokenManager.getSettings();
                    webview.postMessage({
                        command: 'settingsResult',
                        data: settingsResult
                    });
                    break;

                case 'saveSettings':
                    const saveResult = await this.tokenManager.saveSettings(message.settings);
                    webview.postMessage({
                        command: 'saveSettingsResult',
                        data: saveResult
                    });
                    if (saveResult.success) {
                        vscode.window.showInformationMessage(saveResult.message);
                    }
                    break;

                case 'resetSettings':
                    const resetResult = await this.tokenManager.resetSettings();
                    webview.postMessage({
                        command: 'resetSettingsResult',
                        data: resetResult
                    });
                    if (resetResult.success) {
                        vscode.window.showInformationMessage(resetResult.message);
                    }
                    break;

                case 'testApiConnection':
                    const testResult = await this.tokenManager.testApiConnection();
                    webview.postMessage({
                        command: 'testApiResult',
                        data: testResult
                    });
                    vscode.window.showInformationMessage(testResult.message);
                    break;

                // 会话操作（影响官方插件）
                case 'copyAccessToken':
                    const copyResult = await this.tokenManager.copyAccessToken();
                    webview.postMessage({
                        command: 'copyAccessTokenResult',
                        data: copyResult
                    });
                    if (copyResult.success) {
                        vscode.window.showInformationMessage(copyResult.message);
                    }
                    break;

                case 'setAccessToken':
                    const setResult = await this.tokenManager.setAccessToken(message.accessToken);
                    webview.postMessage({
                        command: 'setAccessTokenResult',
                        data: setResult
                    });
                    if (setResult.success) {
                        vscode.window.showInformationMessage('AccessToken设置成功');
                    }
                    break;

                case 'updateAllTokenData':
                    try {
                        const { accessToken, tenantUrl } = message.data;

                        // Success case 2: 仅更新会话数据（不在同一次操作里更新机器码）
                        const updateResult = await this.tokenManager.injectToken(accessToken, tenantUrl);
                        if (!updateResult.success) {
                            throw new Error(`更新会话数据失败: ${updateResult.error}`);
                        }

                        // 发送成功消息（提示用户可单独点击“更新设备码”）
                        webview.postMessage({
                            command: 'updateAllTokenDataResult',
                            data: { success: true, message: '会话数据更新成功！如需更改设备码，请点击“更新设备码”。' }
                        });

                        vscode.window.showInformationMessage('✅ 会话数据更新成功！如需更改设备码，请单独点击“更新设备码”。');

                    } catch (error) {
                        webview.postMessage({
                            command: 'updateAllTokenDataResult',
                            data: { success: false, error: error.message }
                        });
                        vscode.window.showErrorMessage(`更新失败: ${error.message}`);
                    }
                    break;

                case 'loadCurrentTokenToInputs':
                    try {
                        const currentResult = await this.tokenManager.getCurrentToken();
                        if (currentResult.success) {
                            webview.postMessage({
                                command: 'loadTokenToInputsResult',
                                data: {
                                    success: true,
                                    accessToken: currentResult.accessToken || '',
                                    tenantUrl: currentResult.tenantURL || 'https://d5.api.augmentcode.com/'
                                }
                            });
                        } else {
                            webview.postMessage({
                                command: 'loadTokenToInputsResult',
                                data: { success: false, error: currentResult.error }
                            });
                        }
                    } catch (error) {
                        webview.postMessage({
                            command: 'loadTokenToInputsResult',
                            data: { success: false, error: error.message }
                        });
                    }
                    break;

                default:
                    this.logger.warn(`未知命令: ${message.command}`);
            }
        } catch (error) {
            this.logger.error('处理webview消息失败', error);
            webview.postMessage({
                command: 'error',
                data: { error: error.message }
            });
        }
    }

    getWebviewContent() {
        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Token Manager</title>
    <style>
        body { 
            font-family: var(--vscode-font-family); 
            padding: 20px; 
            color: var(--vscode-foreground);
        }
        .section { 
            margin-bottom: 20px; 
            padding: 15px; 
            border: 1px solid var(--vscode-panel-border); 
            border-radius: 5px; 
        }
        .button { 
            background: var(--vscode-button-background); 
            color: var(--vscode-button-foreground); 
            border: none; 
            padding: 8px 16px; 
            margin: 5px; 
            border-radius: 3px; 
            cursor: pointer; 
        }
        .button:hover { 
            background: var(--vscode-button-hoverBackground); 
        }
        .token-item { 
            padding: 10px; 
            margin: 5px 0; 
            background: var(--vscode-editor-background); 
            border-radius: 3px; 
        }
        .current-token { 
            background: var(--vscode-textCodeBlock-background); 
            padding: 10px; 
            border-radius: 3px; 
            font-family: monospace; 
            font-size: 12px; 
        }
        .status { 
            margin: 10px 0; 
            padding: 8px; 
            border-radius: 3px; 
        }
        .success { 
            background: var(--vscode-testing-iconPassed); 
            color: white; 
        }
        .error { 
            background: var(--vscode-testing-iconFailed); 
            color: white; 
        }
    </style>
</head>
<body>
    <h2>🚀 Simple Token Manager</h2>
    
    <div class="section">
        <h3>快速操作</h3>
        <button class="button" onclick="quickUpdate()">🚀 一键更新Token</button>
        <button class="button" onclick="getCurrentToken()">📋 查看当前Token</button>
        <button class="button" onclick="getTokens()">🔄 刷新Token列表</button>
    </div>

    <div class="section">
        <h3>设备管理</h3>
        <div id="deviceInfo">设备码: 加载中...</div>
        <button class="button" onclick="updateDeviceId()">🔧 更新设备码</button>
    </div>

    <div class="section">
        <h3>会话操作（影响官方插件）</h3>
        <button class="button" onclick="copyAccessToken()">📋 复制AccessToken</button>

        <div style="margin-top: 15px;">
            <label>AccessToken:</label>
            <input type="password" id="manualAccessToken" placeholder="输入您的 accessToken..." style="width: 100%; margin-top: 5px; margin-bottom: 10px;">
        </div>

        <div style="margin-bottom: 15px;">
            <label>TenantURL:</label>
            <input type="text" id="manualTenantUrl" placeholder="https://d5.api.augmentcode.com/" style="width: 100%; margin-top: 5px;">
        </div>

        <div style="margin-top: 15px;">
            <button class="button" onclick="updateAllTokenData()" style="background: var(--vscode-button-background); font-weight: bold;">🚀 一键更新 Token + URL + 机器码</button>
            <button class="button" onclick="setManualAccessToken()">✏️ 仅设置 AccessToken</button>
            <button class="button" onclick="loadCurrentTokenToInputs()">📥 加载当前数据到输入框</button>
        </div>
    </div>

    <div class="section">
        <h3>设置（影响官方插件行为）</h3>
        <div style="margin-bottom: 10px;">
            <label>API Base URL:</label>
            <input type="text" id="apiBaseUrl" placeholder="http://localhost:3000" style="width: 100%; margin-top: 5px;">
        </div>
        <div style="margin-bottom: 10px;">
            <label>默认 Tenant URL:</label>
            <input type="text" id="defaultTenantUrl" placeholder="https://d5.api.augmentcode.com/" style="width: 100%; margin-top: 5px;">
        </div>
        <div style="margin-top: 15px;">
            <button class="button" onclick="saveSettings()">💾 保存设置</button>
            <button class="button" onclick="testApiConnection()">🔗 测试连接</button>
            <button class="button" onclick="resetSettings()">🔄 重置默认</button>
        </div>
    </div>

    <div class="section">
        <h3>当前Token信息</h3>
        <div id="currentToken" class="current-token">点击"查看当前Token"获取信息</div>
    </div>

    <div class="section">
        <h3>可用Token列表</h3>
        <div id="tokenList">点击"刷新Token列表"获取可用token</div>
    </div>

    <div id="status"></div>

    <script>
        const vscode = acquireVsCodeApi();

        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.innerHTML = \`<div class="status \${isError ? 'error' : 'success'}">\${message}</div>\`;
            setTimeout(() => status.innerHTML = '', 3000);
        }

        function quickUpdate() {
            vscode.postMessage({ command: 'quickUpdate' });
        }

        function getCurrentToken() {
            vscode.postMessage({ command: 'getCurrentToken' });
        }

        function getTokens() {
            vscode.postMessage({ command: 'getTokens' });
        }

        function getDeviceId() {
            vscode.postMessage({ command: 'getDeviceId' });
        }

        function updateDeviceId() {
            vscode.postMessage({ command: 'updateDeviceId' });
        }

        // 设置相关函数（影响官方插件）
        function getSettings() {
            vscode.postMessage({ command: 'getSettings' });
        }

        function saveSettings() {
            const apiBaseUrl = document.getElementById('apiBaseUrl').value;
            const defaultTenantUrl = document.getElementById('defaultTenantUrl').value;
            vscode.postMessage({
                command: 'saveSettings',
                settings: { apiBaseUrl, defaultTenantUrl }
            });
        }

        function resetSettings() {
            vscode.postMessage({ command: 'resetSettings' });
        }

        function testApiConnection() {
            vscode.postMessage({ command: 'testApiConnection' });
        }

        // 会话操作函数（影响官方插件）
        function copyAccessToken() {
            vscode.postMessage({ command: 'copyAccessToken' });
        }

        function setManualAccessToken() {
            const accessToken = document.getElementById('manualAccessToken').value;
            if (accessToken.trim()) {
                vscode.postMessage({
                    command: 'setAccessToken',
                    accessToken: accessToken.trim()
                });
                document.getElementById('manualAccessToken').value = '';
            }
        }

        function updateAllTokenData() {
            const accessToken = document.getElementById('manualAccessToken').value.trim();
            const tenantUrl = document.getElementById('manualTenantUrl').value.trim();

            // 输入验证
            if (!accessToken) {
                showStatus('请输入 accessToken', true);
                return;
            }

            if (accessToken.length < 10) {
                showStatus('accessToken 长度似乎太短', true);
                return;
            }

            if (!tenantUrl) {
                showStatus('请输入 tenantURL', true);
                return;
            }

            // 验证 URL 格式
            try {
                new URL(tenantUrl);
            } catch (e) {
                showStatus('请输入有效的 tenantURL (例如: https://d5.api.augmentcode.com/)', true);
                return;
            }

            // 发送更新请求
            vscode.postMessage({
                command: 'updateAllTokenData',
                data: {
                    accessToken: accessToken,
                    tenantUrl: tenantUrl
                }
            });

            showStatus('正在更新 Token、URL 和机器码...');
        }

        function loadCurrentTokenToInputs() {
            vscode.postMessage({ command: 'loadCurrentTokenToInputs' });
        }

        function injectToken(accessToken, tenantURL) {
            vscode.postMessage({
                command: 'injectToken',
                accessToken: accessToken,
                tenantURL: tenantURL
            });
        }

        // 处理来自扩展的消息
        window.addEventListener('message', event => {
            const message = event.data;
            
            switch (message.command) {
                case 'currentTokenResult':
                    if (message.data.success) {
                        const token = message.data.accessToken;
                        const displayToken = token.length > 20 ? 
                            token.substring(0, 10) + '...' + token.substring(token.length - 10) : token;
                        document.getElementById('currentToken').innerHTML = \`
                            <strong>Access Token:</strong> \${displayToken}<br>
                            <strong>Tenant URL:</strong> \${message.data.tenantURL}
                        \`;
                    } else {
                        document.getElementById('currentToken').innerHTML = \`错误: \${message.data.error}\`;
                    }
                    break;

                case 'tokensResult':
                    if (message.data.success && message.data.tokens.length > 0) {
                        const tokenList = message.data.tokens.map((token, index) => \`
                            <div class="token-item">
                                <strong>Token \${index + 1}</strong><br>
                                ID: \${token.id || 'N/A'}<br>
                                <button class="button" onclick="injectToken('\${token.accessToken}', '\${token.tenantURL || ''}')">
                                    使用此Token
                                </button>
                            </div>
                        \`).join('');
                        document.getElementById('tokenList').innerHTML = tokenList;
                    } else {
                        document.getElementById('tokenList').innerHTML = \`<div class="error">获取token失败: \${message.data.error || '无可用token'}</div>\`;
                    }
                    break;

                case 'injectResult':
                    if (message.data.success) {
                        showStatus('Token注入成功！');
                        getCurrentToken(); // 刷新当前token显示
                    } else {
                        showStatus(\`注入失败: \${message.data.error}\`, true);
                    }
                    break;

                case 'quickUpdateResult':
                    if (message.data.success) {
                        showStatus(message.data.message);
                        getCurrentToken(); // 刷新当前token显示
                    } else {
                        showStatus(\`快速更新失败: \${message.data.error}\`, true);
                    }
                    break;

                case 'deviceIdResult':
                    if (message.data.success) {
                        document.getElementById('deviceInfo').textContent = \`设备码: \${message.data.deviceId}\`;
                    } else {
                        document.getElementById('deviceInfo').textContent = \`设备码: 获取失败\`;
                    }
                    break;

                case 'updateDeviceResult':
                    if (message.data.success) {
                        showStatus(message.data.message);
                        getDeviceId(); // 刷新设备码显示
                    } else {
                        showStatus(\`设备码更新失败: \${message.data.error}\`, true);
                    }
                    break;

                // 设置相关消息处理
                case 'settingsResult':
                    if (message.data.success) {
                        const settings = message.data.settings;
                        document.getElementById('apiBaseUrl').value = settings.apiBaseUrl || '';
                        document.getElementById('defaultTenantUrl').value = settings.defaultTenantUrl || '';
                    }
                    break;

                case 'saveSettingsResult':
                    if (message.data.success) {
                        showStatus(message.data.message);
                    } else {
                        showStatus(\`保存设置失败: \${message.data.error}\`, true);
                    }
                    break;

                case 'resetSettingsResult':
                    if (message.data.success) {
                        showStatus(message.data.message);
                        getSettings(); // 刷新设置显示
                    } else {
                        showStatus(\`重置设置失败: \${message.data.error}\`, true);
                    }
                    break;

                case 'testApiResult':
                    // 测试结果已在后端显示消息
                    break;

                // 会话操作消息处理
                case 'copyAccessTokenResult':
                    if (message.data.success) {
                        showStatus(message.data.message);
                    } else {
                        showStatus(\`复制失败: \${message.data.error}\`, true);
                    }
                    break;

                case 'setAccessTokenResult':
                    if (message.data.success) {
                        showStatus('AccessToken设置成功');
                        getCurrentToken(); // 刷新当前token显示
                    } else {
                        showStatus(\`设置AccessToken失败: \${message.data.error}\`, true);
                    }
                    break;

                case 'updateAllTokenDataResult':
                    if (message.data.success) {
                        showStatus(message.data.message || '✅ 会话数据更新成功！');
                        getCurrentToken(); // 刷新当前token显示
                        // 设备码不在此处更新，保持与success case 2一致
                    } else {
                        showStatus(\`❌ 更新失败: \${message.data.error}\`, true);
                    }
                    break;

                case 'loadTokenToInputsResult':
                    if (message.data.success) {
                        if (message.data.accessToken) {
                            document.getElementById('manualAccessToken').value = message.data.accessToken;
                        }
                        if (message.data.tenantUrl) {
                            document.getElementById('manualTenantUrl').value = message.data.tenantUrl;
                        }
                        showStatus('当前数据已加载到输入框');
                    }
                    break;

                case 'error':
                    showStatus(\`错误: \${message.data.error}\`, true);
                    break;
            }
        });

        // 页面加载时获取当前token信息、设备码和设置
        getCurrentToken();
        getDeviceId();
        getSettings();
    </script>
</body>
</html>`;
    }

    dispose() {
        if (this.webviewPanel) {
            this.webviewPanel.dispose();
        }
        if (this.statusBarItem) {
            this.statusBarItem.dispose();
        }
        if (this.tokenManager) {
            this.tokenManager.dispose();
        }
        this.logger.info('Custom Features disposed');
    }
}

module.exports = CustomFeatures;
