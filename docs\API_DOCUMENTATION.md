# Token API Documentation

## Base URL
```
http://localhost:9043
```

## Authentication
All protected endpoints require Bearer token authentication:
```
Authorization: Bearer your_secret_password_here_change_this
```

---

## Endpoints

### 1. Health Check

**Endpoint:** `GET /health`  
**Authentication:** None required  
**Description:** Check if the API server is running

#### Request
```http
GET /health
```

#### Response
```json
{
  "success": true,
  "message": "Token API is running",
  "timestamp": "2025-08-19T01:46:50.499Z"
}
```

#### Status Codes
- `200 OK` - Server is running

---

### 2. Get Unused Token

**Endpoint:** `GET /api/tokens`  
**Authentication:** Required  
**Description:** Get and mark as used the oldest unused token

#### Request
```http
GET /api/tokens
Authorization: Bearer your_secret_password_here_change_this
```

#### Response (Success)
```json
{
  "success": true,
  "token": {
    "id": "daf1d7e5-78bd-4d31-bce8-9e03e6d64d76",
    "accessToken": "ccdc3411afa9b74c5c27788ae6f40dfd61f6c3947c0d6c3c83741cafd079e2c2",
    "tenantURL": "https://d8.api.augmentcode.com/",
    "description": "Token from database",
    "createdAt": "2025-08-16T00:13:31.355Z"
  },
  "timestamp": "2025-08-19T01:46:50.499Z"
}
```

#### Response (No Available Tokens)
```json
{
  "success": false,
  "error": "No available tokens",
  "message": "All tokens have been used or no tokens found",
  "timestamp": "2025-08-19T01:46:50.499Z"
}
```

#### Status Codes
- `200 OK` - Token returned successfully
- `404 Not Found` - No available tokens
- `401 Unauthorized` - Invalid authentication
- `500 Internal Server Error` - Server error

---

### 3. Save New Token

**Endpoint:** `POST /api/tokens/save`  
**Authentication:** Required  
**Description:** Save a new token from automation scripts

#### Request
```http
POST /api/tokens/save
Authorization: Bearer your_secret_password_here_change_this
Content-Type: application/json

{
  "access_token": "ccdc3411afa9b74c5c27788ae6f40dfd61f6c3947c0d6c3c83741cafd079e2c2",
  "tenant_url": "https://d8.api.augmentcode.com/",
  "description": "Firefox token from fixed automation",
  "email_note": "<EMAIL>",
  "user_agent": "firefox-fixed-automation",
  "session_id": "firefox_fixed_1755526675",
  "created_timestamp": 1755303211355
}
```

#### Request Body Parameters
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `access_token` | string | ✅ | The authentication token |
| `tenant_url` | string | ✅ | The tenant URL |
| `id` | string | ❌ | Token ID (auto-generated if not provided) |
| `description` | string | ❌ | Token description |
| `email_note` | string | ❌ | Email used for token generation |
| `user_agent` | string | ❌ | User agent identifier |
| `session_id` | string | ❌ | Session identifier |
| `created_timestamp` | number | ❌ | Creation timestamp in milliseconds |

#### Response (Success)
```json
{
  "success": true,
  "message": "Token saved successfully",
  "tokenId": "daf1d7e5-78bd-4d31-bce8-9e03e6d64d76",
  "timestamp": "2025-08-19T01:46:50.499Z"
}
```

#### Response (Validation Error)
```json
{
  "success": false,
  "error": "Missing required fields",
  "message": "access_token and tenant_url are required"
}
```

#### Status Codes
- `200 OK` - Token saved successfully
- `400 Bad Request` - Missing required fields
- `401 Unauthorized` - Invalid authentication
- `500 Internal Server Error` - Server error

---

### 4. Get Valid Token Count

**Endpoint:** `GET /api/tokens/valid-count`  
**Authentication:** Required  
**Description:** Get count of tokens that are unused and still valid (within 7 days minus 2 hour buffer)

#### Request
```http
GET /api/tokens/valid-count
Authorization: Bearer your_secret_password_here_change_this
```

#### Response
```json
{
  "success": true,
  "validCount": 32,
  "timestamp": "2025-08-19T01:46:50.499Z"
}
```

#### Status Codes
- `200 OK` - Count returned successfully
- `401 Unauthorized` - Invalid authentication
- `500 Internal Server Error` - Server error

---

### 5. Get Token Statistics

**Endpoint:** `GET /api/tokens/stats`  
**Authentication:** Required  
**Description:** Get comprehensive token statistics

#### Request
```http
GET /api/tokens/stats
Authorization: Bearer your_secret_password_here_change_this
```

#### Response
```json
{
  "success": true,
  "stats": {
    "total": 34,
    "used": 2,
    "unused": 32,
    "valid": 32
  },
  "timestamp": "2025-08-19T01:46:50.499Z"
}
```

#### Response Fields
| Field | Description |
|-------|-------------|
| `total` | Total number of tokens in database |
| `used` | Number of tokens marked as used |
| `unused` | Number of tokens not yet used |
| `valid` | Number of unused tokens still within validity period |

#### Status Codes
- `200 OK` - Statistics returned successfully
- `401 Unauthorized` - Invalid authentication
- `500 Internal Server Error` - Server error

---

### 6. Trigger Automation Script

**Endpoint:** `POST /api/tokens/trigger-automation`  
**Authentication:** Required  
**Description:** Manually trigger the automation script

#### Request
```http
POST /api/tokens/trigger-automation
Authorization: Bearer your_secret_password_here_change_this
Content-Type: application/json

{
  "count": 3
}
```

#### Request Body Parameters
| Field | Type | Required | Default | Description |
|-------|------|----------|---------|-------------|
| `count` | number | ❌ | 1 | Number of script instances to run |

#### Response
```json
{
  "success": true,
  "message": "Triggering automation script 3 time(s)",
  "timestamp": "2025-08-19T01:46:50.499Z"
}
```

#### Status Codes
- `200 OK` - Automation triggered successfully
- `401 Unauthorized` - Invalid authentication
- `500 Internal Server Error` - Server error

---

## Error Responses

All error responses follow this format:
```json
{
  "success": false,
  "error": "Error Type",
  "message": "Detailed error description",
  "timestamp": "2025-08-19T01:46:50.499Z"
}
```

### Common Error Types
- `Unauthorized` - Invalid or missing authentication token
- `Missing required fields` - Required request body fields are missing
- `No available tokens` - No unused tokens found
- `Internal server error` - Server-side error occurred

---

## Rate Limiting
Currently no rate limiting is implemented.

## CORS
CORS is enabled for all origins.

## Content Type
All POST requests must use `Content-Type: application/json`.
